#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 测试Augment Plugin Cleaner的各个功能模块
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import patch, MagicMock
import json

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入要测试的模块
try:
    from config import SUPPORTED_IDES, CLEANUP_CATEGORIES
    from utils import (
        print_colored, safe_delete_file, safe_delete_directory,
        format_size, create_file_hash
    )
    from ide_cleaners import IDECleaner
    from registry_cleaner import RegistryCleaner
    from validator import CleanupValidator
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


class TestUtils(unittest.TestCase):
    """测试工具函数"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.test_file = self.test_dir / "test_file.txt"
        self.test_file.write_text("test content")
        
    def tearDown(self):
        """清理测试环境"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            
    def test_safe_delete_file(self):
        """测试安全删除文件"""
        result = safe_delete_file(self.test_file, backup=False)
        self.assertTrue(result['success'])
        self.assertFalse(self.test_file.exists())
        
    def test_safe_delete_directory(self):
        """测试安全删除目录"""
        result = safe_delete_directory(self.test_dir, backup=False)
        self.assertTrue(result['success'])
        self.assertFalse(self.test_dir.exists())
        
    def test_format_size(self):
        """测试文件大小格式化"""
        self.assertEqual(format_size(0), "0 B")
        self.assertEqual(format_size(1024), "1.00 KB")
        self.assertEqual(format_size(1024 * 1024), "1.00 MB")
        
    def test_create_file_hash(self):
        """测试文件哈希创建"""
        hash_value = create_file_hash(self.test_file)
        self.assertIsNotNone(hash_value)
        self.assertEqual(len(hash_value), 32)  # MD5哈希长度


class TestIDECleaner(unittest.TestCase):
    """测试IDE清理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.cleaner = IDECleaner(verbose=False, unsafe=False)
        self.test_dir = Path(tempfile.mkdtemp())
        
    def tearDown(self):
        """清理测试环境"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            
    def test_cleaner_initialization(self):
        """测试清理器初始化"""
        self.assertFalse(self.cleaner.verbose)
        self.assertFalse(self.cleaner.unsafe)
        self.assertEqual(self.cleaner.stats['files_deleted'], 0)
        
    @patch('ide_cleaners.is_process_running')
    def test_check_ide_running(self, mock_is_running):
        """测试IDE运行状态检查"""
        mock_is_running.return_value = False
        result = self.cleaner._check_ide_running('jetbrains')
        self.assertFalse(result)
        
    def test_clean_ide_invalid(self):
        """测试清理不支持的IDE"""
        result = self.cleaner.clean_ide('invalid_ide', ['config'])
        self.assertGreater(len(result['errors']), 0)


class TestValidator(unittest.TestCase):
    """测试验证器"""
    
    def setUp(self):
        """设置测试环境"""
        self.validator = CleanupValidator(verbose=False)
        
    def test_validator_initialization(self):
        """测试验证器初始化"""
        self.assertFalse(self.validator.verbose)
        self.assertTrue(self.validator.validation_results['success'])
        self.assertEqual(len(self.validator.validation_results['issues']), 0)
        
    def test_validate_cleanup(self):
        """测试清理验证"""
        result = self.validator.validate_cleanup()
        self.assertIn('success', result)
        self.assertIn('issues', result)
        self.assertIn('scan_summary', result)


class TestConfig(unittest.TestCase):
    """测试配置"""
    
    def test_supported_ides(self):
        """测试支持的IDE配置"""
        self.assertIn('jetbrains', SUPPORTED_IDES)
        self.assertIn('vscode', SUPPORTED_IDES)
        self.assertIn('cursor', SUPPORTED_IDES)
        
        for ide_name, ide_config in SUPPORTED_IDES.items():
            self.assertIn('name', ide_config)
            self.assertIn('description', ide_config)
            self.assertIn('config_paths', ide_config)
            
    def test_cleanup_categories(self):
        """测试清理类别配置"""
        expected_categories = ['config', 'plugins', 'projects', 'cache', 'userdata', 'chats', 'registry']
        
        for category in expected_categories:
            self.assertIn(category, CLEANUP_CATEGORIES)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        
        # 创建模拟的Augment文件结构
        self.create_mock_augment_files()
        
    def tearDown(self):
        """清理测试环境"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            
    def create_mock_augment_files(self):
        """创建模拟的Augment文件"""
        # 创建配置文件
        config_dir = self.test_dir / "config"
        config_dir.mkdir(parents=True)
        (config_dir / "augment.json").write_text('{"test": true}')
        
        # 创建缓存文件
        cache_dir = self.test_dir / "cache" / "augment"
        cache_dir.mkdir(parents=True)
        (cache_dir / "cache_file.tmp").write_text("cache data")
        
        # 创建插件目录
        plugin_dir = self.test_dir / "plugins" / "augment-plugin"
        plugin_dir.mkdir(parents=True)
        (plugin_dir / "plugin.jar").write_text("plugin data")
        
    def test_full_cleanup_simulation(self):
        """测试完整清理流程模拟"""
        # 这里可以添加完整的清理流程测试
        # 由于涉及系统文件操作，这里只做基本验证
        
        cleaner = IDECleaner(verbose=False, unsafe=False)
        validator = CleanupValidator(verbose=False)
        
        # 验证清理器和验证器都能正常初始化
        self.assertIsNotNone(cleaner)
        self.assertIsNotNone(validator)


def run_performance_test():
    """运行性能测试"""
    print_colored("🚀 运行性能测试...", 'cyan')
    
    import time
    
    # 测试大量文件的处理性能
    test_dir = Path(tempfile.mkdtemp())
    
    try:
        # 创建大量测试文件
        start_time = time.time()
        
        for i in range(1000):
            test_file = test_dir / f"test_file_{i}.txt"
            test_file.write_text(f"test content {i}")
            
        creation_time = time.time() - start_time
        print_colored(f"  创建1000个文件耗时: {creation_time:.2f}秒", 'white')
        
        # 测试删除性能
        start_time = time.time()
        
        cleaner = IDECleaner(verbose=False, unsafe=False)
        for test_file in test_dir.glob("*.txt"):
            safe_delete_file(test_file, backup=False)
            
        deletion_time = time.time() - start_time
        print_colored(f"  删除1000个文件耗时: {deletion_time:.2f}秒", 'white')
        
        print_colored("✅ 性能测试完成", 'green')
        
    finally:
        if test_dir.exists():
            shutil.rmtree(test_dir)


def run_compatibility_test():
    """运行兼容性测试"""
    print_colored("🔧 运行兼容性测试...", 'cyan')
    
    import platform
    
    system = platform.system()
    python_version = platform.python_version()
    
    print_colored(f"  系统平台: {system}", 'white')
    print_colored(f"  Python版本: {python_version}", 'white')
    
    # 测试模块导入
    modules_to_test = [
        'pathlib', 'json', 'time', 'datetime', 'platform',
        'ctypes', 'subprocess', 'shutil', 'glob', 're', 'hashlib'
    ]
    
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print_colored(f"  ✓ {module_name}", 'green')
        except ImportError:
            failed_imports.append(module_name)
            print_colored(f"  ✗ {module_name}", 'red')
            
    if failed_imports:
        print_colored(f"❌ 兼容性测试失败: {', '.join(failed_imports)}", 'red')
        return False
    else:
        print_colored("✅ 兼容性测试通过", 'green')
        return True


def main():
    """主测试函数"""
    print_colored("🧪 开始测试 Augment Plugin Cleaner", 'cyan')
    print_colored("="*60, 'white')
    
    # 运行单元测试
    print_colored("\n📋 运行单元测试...", 'cyan')
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [TestUtils, TestIDECleaner, TestValidator, TestConfig, TestIntegration]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
        
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 运行性能测试
    print_colored("\n" + "="*60, 'white')
    run_performance_test()
    
    # 运行兼容性测试
    print_colored("\n" + "="*60, 'white')
    compatibility_ok = run_compatibility_test()
    
    # 总结
    print_colored("\n" + "="*60, 'white')
    print_colored("📊 测试总结", 'cyan')
    
    if result.wasSuccessful() and compatibility_ok:
        print_colored("✅ 所有测试通过!", 'green')
        print_colored(f"运行了 {result.testsRun} 个测试", 'white')
        return True
    else:
        print_colored("❌ 测试失败!", 'red')
        print_colored(f"失败: {len(result.failures)}, 错误: {len(result.errors)}", 'red')
        return False


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print_colored("\n⚠️  测试被用户中断", 'yellow')
        sys.exit(0)
    except Exception as e:
        print_colored(f"\n❌ 测试异常: {e}", 'red')
        sys.exit(1)

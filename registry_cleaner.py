# -*- coding: utf-8 -*-
"""
注册表清理模块 - 实现Windows注册表中Augment相关项的清理
"""

import os
import sys
import platform
from typing import List, Dict, Any, Optional, Tuple
import json
import time

# Windows注册表操作
if platform.system() == 'Windows':
    try:
        import winreg
    except ImportError:
        winreg = None
else:
    winreg = None

from config import REGISTRY_PATHS, IS_WINDOWS
from utils import print_colored, save_json, load_json


class RegistryCleaner:
    """Windows注册表清理器"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.stats = {
            'registry_keys_deleted': 0,
            'registry_values_deleted': 0,
            'errors': []
        }
        self.backup_data = []
        
        if not IS_WINDOWS:
            if verbose:
                print_colored("⚠️  当前系统不是Windows，跳过注册表清理", 'yellow')
        elif winreg is None:
            if verbose:
                print_colored("⚠️  无法导入winreg模块，跳过注册表清理", 'yellow')
                
    def clean_registry(self) -> Dict[str, Any]:
        """清理注册表中的Augment相关项"""
        if not IS_WINDOWS or winreg is None:
            return self.stats
            
        if self.verbose:
            print_colored("🗃️  开始清理注册表...", 'cyan')
            
        # 备份注册表项
        self._backup_registry_keys()
        
        # 清理各个注册表路径
        for registry_path in REGISTRY_PATHS:
            self._clean_registry_path(registry_path)
            
        if self.verbose:
            print_colored("✅ 注册表清理完成", 'green')
            
        return self.stats.copy()
        
    def _backup_registry_keys(self) -> None:
        """备份要删除的注册表项"""
        if self.verbose:
            print_colored("  📋 备份注册表项...", 'white')
            
        backup_file = f"registry_backup_{int(time.time())}.json"
        
        for registry_path in REGISTRY_PATHS:
            try:
                hive, subkey = self._parse_registry_path(registry_path)
                if hive is None:
                    continue
                    
                # 查找匹配的键
                matching_keys = self._find_matching_keys(hive, subkey)
                
                for key_path in matching_keys:
                    key_data = self._export_registry_key(hive, key_path)
                    if key_data:
                        self.backup_data.append({
                            'hive': self._hive_to_string(hive),
                            'path': key_path,
                            'data': key_data,
                            'timestamp': time.time()
                        })
                        
            except Exception as e:
                if self.verbose:
                    print_colored(f"    备份注册表项失败: {registry_path} - {e}", 'yellow')
                    
        # 保存备份数据
        if self.backup_data:
            try:
                save_json(self.backup_data, backup_file)
                if self.verbose:
                    print_colored(f"  ✓ 注册表备份已保存: {backup_file}", 'green')
            except Exception as e:
                if self.verbose:
                    print_colored(f"  ✗ 保存注册表备份失败: {e}", 'red')
                    
    def _clean_registry_path(self, registry_path: str) -> None:
        """清理指定的注册表路径"""
        try:
            hive, subkey = self._parse_registry_path(registry_path)
            if hive is None:
                return
                
            # 查找匹配的键
            matching_keys = self._find_matching_keys(hive, subkey)
            
            for key_path in matching_keys:
                if self._delete_registry_key(hive, key_path):
                    self.stats['registry_keys_deleted'] += 1
                    if self.verbose:
                        print_colored(f"    ✓ 删除注册表项: {self._hive_to_string(hive)}\\{key_path}", 'green')
                else:
                    error_msg = f"删除注册表项失败: {self._hive_to_string(hive)}\\{key_path}"
                    self.stats['errors'].append(error_msg)
                    if self.verbose:
                        print_colored(f"    ✗ {error_msg}", 'red')
                        
        except Exception as e:
            error_msg = f"处理注册表路径失败: {registry_path} - {e}"
            self.stats['errors'].append(error_msg)
            if self.verbose:
                print_colored(f"    ✗ {error_msg}", 'red')
                
    def _parse_registry_path(self, registry_path: str) -> Tuple[Optional[int], str]:
        """解析注册表路径"""
        parts = registry_path.split('\\', 1)
        if len(parts) != 2:
            return None, ""
            
        hive_name, subkey = parts
        
        hive_map = {
            'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER,
            'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE,
            'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT,
            'HKEY_USERS': winreg.HKEY_USERS,
            'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG,
        }
        
        return hive_map.get(hive_name), subkey
        
    def _hive_to_string(self, hive: int) -> str:
        """将注册表hive转换为字符串"""
        hive_map = {
            winreg.HKEY_CURRENT_USER: 'HKEY_CURRENT_USER',
            winreg.HKEY_LOCAL_MACHINE: 'HKEY_LOCAL_MACHINE',
            winreg.HKEY_CLASSES_ROOT: 'HKEY_CLASSES_ROOT',
            winreg.HKEY_USERS: 'HKEY_USERS',
            winreg.HKEY_CURRENT_CONFIG: 'HKEY_CURRENT_CONFIG',
        }
        return hive_map.get(hive, 'UNKNOWN')
        
    def _find_matching_keys(self, hive: int, subkey_pattern: str) -> List[str]:
        """查找匹配的注册表键"""
        matching_keys = []
        
        try:
            # 如果模式包含通配符
            if '*' in subkey_pattern:
                # 获取基础路径
                base_parts = subkey_pattern.split('*')
                if len(base_parts) >= 2:
                    base_path = base_parts[0].rstrip('\\')
                    pattern = base_parts[1].lstrip('\\') if len(base_parts) > 1 else ""
                    
                    # 枚举基础路径下的子键
                    try:
                        with winreg.OpenKey(hive, base_path) as base_key:
                            i = 0
                            while True:
                                try:
                                    subkey_name = winreg.EnumKey(base_key, i)
                                    full_path = f"{base_path}\\{subkey_name}"
                                    
                                    # 检查是否匹配Augment相关模式
                                    if self._is_augment_related(subkey_name):
                                        if pattern:
                                            # 检查子路径是否存在
                                            try:
                                                with winreg.OpenKey(hive, f"{full_path}\\{pattern}"):
                                                    matching_keys.append(f"{full_path}\\{pattern}")
                                            except FileNotFoundError:
                                                pass
                                        else:
                                            matching_keys.append(full_path)
                                            
                                    i += 1
                                except OSError:
                                    break
                                    
                    except FileNotFoundError:
                        pass
            else:
                # 直接检查键是否存在
                try:
                    with winreg.OpenKey(hive, subkey_pattern):
                        matching_keys.append(subkey_pattern)
                except FileNotFoundError:
                    pass
                    
        except Exception as e:
            if self.verbose:
                print_colored(f"    查找注册表键失败: {subkey_pattern} - {e}", 'yellow')
                
        return matching_keys
        
    def _is_augment_related(self, key_name: str) -> bool:
        """检查键名是否与Augment相关"""
        key_name_lower = key_name.lower()
        augment_keywords = ['augment', 'augmentcode']
        
        return any(keyword in key_name_lower for keyword in augment_keywords)
        
    def _delete_registry_key(self, hive: int, subkey: str) -> bool:
        """删除注册表键"""
        try:
            # 递归删除子键
            self._delete_subkeys_recursive(hive, subkey)
            
            # 删除主键
            winreg.DeleteKey(hive, subkey)
            return True
            
        except FileNotFoundError:
            # 键不存在，认为删除成功
            return True
        except Exception as e:
            if self.verbose:
                print_colored(f"    删除注册表键失败: {subkey} - {e}", 'red')
            return False
            
    def _delete_subkeys_recursive(self, hive: int, subkey: str) -> None:
        """递归删除子键"""
        try:
            with winreg.OpenKey(hive, subkey) as key:
                # 获取所有子键名
                subkey_names = []
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        subkey_names.append(subkey_name)
                        i += 1
                    except OSError:
                        break
                        
                # 递归删除每个子键
                for subkey_name in subkey_names:
                    full_subkey = f"{subkey}\\{subkey_name}"
                    self._delete_subkeys_recursive(hive, full_subkey)
                    try:
                        winreg.DeleteKey(hive, full_subkey)
                    except:
                        pass
                        
        except FileNotFoundError:
            pass
        except Exception:
            pass
            
    def _export_registry_key(self, hive: int, subkey: str) -> Optional[Dict]:
        """导出注册表键数据"""
        try:
            with winreg.OpenKey(hive, subkey) as key:
                key_data = {
                    'values': {},
                    'subkeys': {}
                }
                
                # 导出值
                i = 0
                while True:
                    try:
                        value_name, value_data, value_type = winreg.EnumValue(key, i)
                        key_data['values'][value_name] = {
                            'data': value_data,
                            'type': value_type
                        }
                        i += 1
                    except OSError:
                        break
                        
                # 导出子键（只导出一层）
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        # 递归导出子键会很复杂，这里只记录子键名
                        key_data['subkeys'][subkey_name] = {}
                        i += 1
                    except OSError:
                        break
                        
                return key_data
                
        except Exception:
            return None
            
    def restore_registry_backup(self, backup_file: str) -> bool:
        """从备份文件恢复注册表"""
        try:
            backup_data = load_json(backup_file)
            if not backup_data:
                return False
                
            if self.verbose:
                print_colored("🔄 恢复注册表备份...", 'cyan')
                
            for item in backup_data:
                hive_name = item['hive']
                key_path = item['path']
                key_data = item['data']
                
                hive = self._string_to_hive(hive_name)
                if hive is None:
                    continue
                    
                if self._restore_registry_key(hive, key_path, key_data):
                    if self.verbose:
                        print_colored(f"  ✓ 恢复注册表项: {hive_name}\\{key_path}", 'green')
                else:
                    if self.verbose:
                        print_colored(f"  ✗ 恢复注册表项失败: {hive_name}\\{key_path}", 'red')
                        
            return True
            
        except Exception as e:
            if self.verbose:
                print_colored(f"恢复注册表备份失败: {e}", 'red')
            return False
            
    def _string_to_hive(self, hive_name: str) -> Optional[int]:
        """将字符串转换为注册表hive"""
        hive_map = {
            'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER,
            'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE,
            'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT,
            'HKEY_USERS': winreg.HKEY_USERS,
            'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG,
        }
        return hive_map.get(hive_name)
        
    def _restore_registry_key(self, hive: int, subkey: str, key_data: Dict) -> bool:
        """恢复注册表键"""
        try:
            # 创建键
            with winreg.CreateKey(hive, subkey) as key:
                # 恢复值
                for value_name, value_info in key_data.get('values', {}).items():
                    winreg.SetValueEx(
                        key, 
                        value_name, 
                        0, 
                        value_info['type'], 
                        value_info['data']
                    )
                    
            return True
            
        except Exception:
            return False

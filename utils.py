# -*- coding: utf-8 -*-
"""
工具函数模块 - 提供通用的工具函数
"""

import os
import sys
import shutil
import platform
import ctypes
import subprocess
from pathlib import Path
from typing import List, Optional, Union, Dict, Any
import glob
import time
import hashlib
import json

from config import COLORS, IS_WINDOWS, USER_HOME


def print_colored(text: str, color: str = 'white', bold: bool = False) -> None:
    """打印彩色文本"""
    color_code = COLORS.get(color.lower(), COLORS['white'])
    bold_code = COLORS['bold'] if bold else ''
    reset_code = COLORS['reset']
    
    print(f"{bold_code}{color_code}{text}{reset_code}")


def confirm_action(message: str, default: bool = False) -> bool:
    """确认操作"""
    suffix = " [Y/n]" if default else " [y/N]"
    
    while True:
        try:
            response = input(f"{message}{suffix}: ").strip().lower()
            
            if not response:
                return default
                
            if response in ['y', 'yes', '是', 'y']:
                return True
            elif response in ['n', 'no', '否', 'n']:
                return False
            else:
                print_colored("请输入 y/yes 或 n/no", 'yellow')
                
        except (KeyboardInterrupt, EOFError):
            print_colored("\n操作已取消", 'yellow')
            return False


def is_admin() -> bool:
    """检查是否以管理员权限运行"""
    if IS_WINDOWS:
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    else:
        return os.geteuid() == 0


def get_user_home() -> Path:
    """获取用户主目录"""
    return USER_HOME


def safe_delete_file(file_path: Union[str, Path], backup: bool = True) -> Dict[str, Any]:
    """安全删除文件"""
    file_path = Path(file_path)
    result = {
        'success': False,
        'size': 0,
        'error': None,
        'backed_up': False
    }
    
    try:
        if not file_path.exists():
            result['success'] = True
            return result
            
        # 获取文件大小
        result['size'] = file_path.stat().st_size
        
        # 备份文件
        if backup:
            backup_result = backup_file(file_path)
            result['backed_up'] = backup_result['success']
            
        # 删除文件
        if file_path.is_file():
            file_path.unlink()
        elif file_path.is_dir():
            shutil.rmtree(file_path)
            
        result['success'] = True
        
    except PermissionError as e:
        result['error'] = f"权限不足: {e}"
    except FileNotFoundError:
        result['success'] = True  # 文件不存在也算成功
    except Exception as e:
        result['error'] = f"删除失败: {e}"
        
    return result


def safe_delete_directory(dir_path: Union[str, Path], backup: bool = True) -> Dict[str, Any]:
    """安全删除目录"""
    dir_path = Path(dir_path)
    result = {
        'success': False,
        'files_deleted': 0,
        'size_freed': 0,
        'error': None,
        'backed_up': False
    }
    
    try:
        if not dir_path.exists():
            result['success'] = True
            return result
            
        # 计算目录大小和文件数量
        total_size = 0
        file_count = 0
        
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                file_path = Path(root) / file
                try:
                    total_size += file_path.stat().st_size
                    file_count += 1
                except:
                    pass
                    
        result['files_deleted'] = file_count
        result['size_freed'] = total_size
        
        # 备份目录
        if backup:
            backup_result = backup_directory(dir_path)
            result['backed_up'] = backup_result['success']
            
        # 删除目录
        shutil.rmtree(dir_path)
        result['success'] = True
        
    except PermissionError as e:
        result['error'] = f"权限不足: {e}"
    except FileNotFoundError:
        result['success'] = True  # 目录不存在也算成功
    except Exception as e:
        result['error'] = f"删除失败: {e}"
        
    return result


def backup_file(file_path: Union[str, Path]) -> Dict[str, Any]:
    """备份文件"""
    from config import CLEANUP_CONFIG
    
    file_path = Path(file_path)
    result = {'success': False, 'backup_path': None, 'error': None}
    
    try:
        if not file_path.exists():
            result['success'] = True
            return result
            
        backup_dir = CLEANUP_CONFIG['backup_dir']
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建备份文件名
        timestamp = int(time.time())
        backup_name = f"{file_path.name}.{timestamp}.backup"
        backup_path = backup_dir / backup_name
        
        # 检查备份大小限制
        file_size = file_path.stat().st_size
        if file_size > CLEANUP_CONFIG['max_backup_size']:
            result['error'] = f"文件太大，跳过备份: {file_size} bytes"
            result['success'] = True  # 跳过备份但不算失败
            return result
            
        # 复制文件
        shutil.copy2(file_path, backup_path)
        result['success'] = True
        result['backup_path'] = backup_path
        
    except Exception as e:
        result['error'] = f"备份失败: {e}"
        
    return result


def backup_directory(dir_path: Union[str, Path]) -> Dict[str, Any]:
    """备份目录"""
    from config import CLEANUP_CONFIG
    
    dir_path = Path(dir_path)
    result = {'success': False, 'backup_path': None, 'error': None}
    
    try:
        if not dir_path.exists():
            result['success'] = True
            return result
            
        backup_dir = CLEANUP_CONFIG['backup_dir']
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建备份目录名
        timestamp = int(time.time())
        backup_name = f"{dir_path.name}.{timestamp}.backup"
        backup_path = backup_dir / backup_name
        
        # 检查目录大小
        dir_size = get_directory_size(dir_path)
        if dir_size > CLEANUP_CONFIG['max_backup_size']:
            result['error'] = f"目录太大，跳过备份: {dir_size} bytes"
            result['success'] = True  # 跳过备份但不算失败
            return result
            
        # 复制目录
        shutil.copytree(dir_path, backup_path)
        result['success'] = True
        result['backup_path'] = backup_path
        
    except Exception as e:
        result['error'] = f"备份失败: {e}"
        
    return result


def get_directory_size(dir_path: Union[str, Path]) -> int:
    """获取目录大小"""
    dir_path = Path(dir_path)
    total_size = 0
    
    try:
        for root, dirs, files in os.walk(dir_path):
            for file in files:
                file_path = Path(root) / file
                try:
                    total_size += file_path.stat().st_size
                except:
                    pass
    except:
        pass
        
    return total_size


def find_files_by_pattern(base_path: Union[str, Path], patterns: List[str]) -> List[Path]:
    """根据模式查找文件"""
    base_path = Path(base_path)
    found_files = []
    
    if not base_path.exists():
        return found_files
        
    for pattern in patterns:
        try:
            # 使用glob查找文件
            if base_path.is_dir():
                matches = list(base_path.rglob(pattern))
                found_files.extend(matches)
        except Exception:
            pass
            
    # 去重并返回
    return list(set(found_files))


def is_process_running(process_name: str) -> bool:
    """检查进程是否正在运行"""
    try:
        if IS_WINDOWS:
            # Windows
            result = subprocess.run(
                ['tasklist', '/FI', f'IMAGENAME eq {process_name}'],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            return process_name.lower() in result.stdout.lower()
        else:
            # Linux/macOS
            result = subprocess.run(
                ['pgrep', '-f', process_name],
                capture_output=True,
                text=True
            )
            return result.returncode == 0
    except:
        return False


def kill_process(process_name: str) -> bool:
    """终止进程"""
    try:
        if IS_WINDOWS:
            # Windows
            result = subprocess.run(
                ['taskkill', '/F', '/IM', process_name],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            return result.returncode == 0
        else:
            # Linux/macOS
            result = subprocess.run(
                ['pkill', '-f', process_name],
                capture_output=True,
                text=True
            )
            return result.returncode == 0
    except:
        return False


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
        
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
        
    return f"{size_bytes:.2f} {size_names[i]}"


def create_file_hash(file_path: Union[str, Path]) -> Optional[str]:
    """创建文件哈希"""
    try:
        file_path = Path(file_path)
        if not file_path.exists() or not file_path.is_file():
            return None
            
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
                
        return hash_md5.hexdigest()
    except:
        return None


def save_json(data: Any, file_path: Union[str, Path]) -> bool:
    """保存JSON数据"""
    try:
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
            
        return True
    except:
        return False


def load_json(file_path: Union[str, Path]) -> Optional[Any]:
    """加载JSON数据"""
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return None
            
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return None


def expand_path_variables(path_str: str) -> str:
    """展开路径变量"""
    # 展开环境变量
    path_str = os.path.expandvars(path_str)
    # 展开用户目录
    path_str = os.path.expanduser(path_str)
    return path_str

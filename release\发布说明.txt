╔══════════════════════════════════════════════════════════════╗
║                 Augment Plugin Cleaner v1.0                 ║
║                    发布说明和使用指南                        ║
╚══════════════════════════════════════════════════════════════╝

🎉 恭喜！Augment Plugin Cleaner 已成功打包完成！

📦 发布信息
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• 版本: v1.0.0
• 文件大小: 约 8.3 MB
• 构建时间: 2025-01-30
• 支持平台: Windows 7/8/10/11 (x64)
• 文件名: augment-cleaner.exe

🚀 快速开始
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
1. 右键点击 augment-cleaner.exe
2. 选择"以管理员身份运行" (推荐)
3. 按照程序提示选择要清理的IDE和项目
4. 确认清理操作
5. 查看清理报告

💡 使用建议
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• 建议在清理前关闭所有相关的IDE
• 首次使用建议选择部分清理项目进行测试
• 重要项目建议先备份
• 使用管理员权限可获得最佳清理效果

🔧 命令行使用
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
在命令提示符中运行：

# 显示帮助信息
augment-cleaner.exe --help

# 交互式清理（推荐新手使用）
augment-cleaner.exe

# 只清理JetBrains IDE
augment-cleaner.exe --ide jetbrains

# 只清理VS Code
augment-cleaner.exe --ide vscode

# 清理多个IDE
augment-cleaner.exe --ide jetbrains vscode

# 只清理配置和缓存
augment-cleaner.exe --categories config,cache

# 详细输出模式
augment-cleaner.exe --verbose

# 彻底清理模式（谨慎使用）
augment-cleaner.exe --unsafe

# 保存清理报告
augment-cleaner.exe --report-file report.txt

🎯 支持的IDE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ JetBrains系列
   • IntelliJ IDEA
   • PyCharm
   • WebStorm
   • PhpStorm
   • 其他JetBrains IDE

✅ Visual Studio Code
✅ Cursor Editor

🗂️ 清理项目
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ 配置文件 - options目录中的Augment相关配置
✅ 插件数据 - 插件目录中的缓存和数据文件
✅ 项目数据 - .IDE目录中的Augment文件
✅ 缓存文件 - 各种临时和缓存文件
✅ 用户数据 - .jetbrains和.augmentcode目录
✅ 聊天记录 - Augment聊天历史记录
✅ 注册表项 - Windows注册表中的相关项

🔍 清理验证
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
程序会自动验证清理效果：
• 残留文件扫描
• 配置重置确认
• 清理统计报告
• 操作完整性检查

⚠️ 注意事项
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• 程序会在删除前自动备份重要文件
• 备份文件保存在用户目录的 .augment_cleaner_backup 文件夹中
• 使用 --unsafe 模式时请格外谨慎
• 如遇到权限问题，请以管理员身份运行
• 建议在清理前关闭所有相关IDE和编辑器

🛠️ 故障排除
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
如果遇到问题：

1. 权限不足
   → 右键"以管理员身份运行"

2. 清理不完整
   → 使用 --verbose 查看详细信息
   → 尝试 --unsafe 模式

3. 文件被占用
   → 关闭相关程序后重试
   → 重启计算机后再次运行

4. 程序无法启动
   → 检查是否有杀毒软件拦截
   → 确认Windows版本兼容性

📊 清理报告示例
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
清理完成后，程序会显示详细报告：

╔══════════════════════════════════════════════════════════════╗
║                        清理报告                              ║
╠══════════════════════════════════════════════════════════════╣
║ 清理时间: 2025-01-30 14:30:25                               ║
║ 系统平台: Windows                                            ║
║                                                              ║
║ 📊 清理统计:                                                 ║
║   • 删除文件: 156        个                                  ║
║   • 删除目录: 23         个                                  ║
║   • 删除注册表项: 8      个                                  ║
║   • 释放空间: 245.67 MB                                      ║
║                                                              ║
║ ✅ 验证状态: 通过                                            ║
╚══════════════════════════════════════════════════════════════╝

🎉 使用愉快！
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
感谢使用 Augment Plugin Cleaner！
如有问题或建议，请查看 README.md 文件获取更多信息。

让你的IDE重新焕发活力！ ⚡

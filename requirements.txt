# Augment Plugin Cleaner 依赖包
# 基础依赖
pathlib2>=2.3.7; python_version<"3.4"

# Windows颜色支持
colorama>=0.4.4

# 打包工具
pyinstaller>=5.0.0

# 可选依赖 - 用于更好的用户体验
# 进度条显示
tqdm>=4.64.0

# 更好的命令行参数解析
click>=8.0.0

# JSON处理增强
ujson>=5.0.0

# 日志处理
loguru>=0.6.0

# 配置文件处理
pyyaml>=6.0

# 系统信息获取
psutil>=5.9.0

# 文件操作增强
send2trash>=1.8.0

# 加密和哈希
cryptography>=3.4.8

# 网络请求（如果需要在线功能）
requests>=2.28.0

# 时间处理
python-dateutil>=2.8.2

# 正则表达式增强
regex>=2022.7.9

# 文件监控
watchdog>=2.1.9

# 压缩和解压
zipfile36>=0.1.3

# 系统服务管理
pywin32>=304; sys_platform=="win32"

# 跨平台路径处理
pathspec>=0.9.0

# 配置管理
configparser>=5.2.0

# 命令行界面增强
rich>=12.5.1

# 文件类型检测
python-magic>=0.4.27

# 内存优化
pympler>=0.9

# 多线程处理
concurrent-futures>=3.1.1; python_version<"3.2"

# 异步处理
asyncio>=3.4.3; python_version<"3.4"

# 数据验证
cerberus>=1.3.4

# 单元测试
pytest>=7.1.2
pytest-cov>=3.0.0

# 代码质量
flake8>=5.0.4
black>=22.6.0

# 文档生成
sphinx>=5.1.1

# 性能分析
memory-profiler>=0.60.0
line-profiler>=3.5.1

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建脚本 - 将Python程序打包成exe文件
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path
import json
import time
from datetime import datetime

# 构建配置
BUILD_CONFIG = {
    'app_name': 'augment-cleaner',
    'main_script': 'augment_cleaner.py',
    'version': '1.0.0',
    'description': 'Augment Plugin Cleaner - 清理各个IDE中的Augment插件缓存和数据',
    'author': 'Augment Cleaner Team',
    'icon': None,  # 可以添加图标文件路径
    'console': True,  # 是否显示控制台窗口
    'onefile': True,  # 是否打包成单个文件
    'upx': False,  # 是否使用UPX压缩
    'debug': False,  # 是否包含调试信息
}

# 需要包含的数据文件
DATA_FILES = [
    # ('source_path', 'dest_path_in_exe')
]

# 需要包含的模块
HIDDEN_IMPORTS = [
    'winreg',  # Windows注册表模块
    'colorama',  # 颜色支持
    'pathlib',  # 路径处理
    'json',  # JSON处理
    'time',  # 时间处理
    'datetime',  # 日期时间
    'platform',  # 平台信息
    'ctypes',  # 系统调用
    'subprocess',  # 子进程
    'shutil',  # 文件操作
    'glob',  # 文件匹配
    're',  # 正则表达式
    'hashlib',  # 哈希计算
]

# 排除的模块（减小文件大小）
EXCLUDES = [
    'tkinter',  # GUI库
    'matplotlib',  # 绘图库
    'numpy',  # 数值计算
    'pandas',  # 数据分析
    'scipy',  # 科学计算
    'PIL',  # 图像处理
    'cv2',  # OpenCV
    'tensorflow',  # 机器学习
    'torch',  # PyTorch
    'jupyter',  # Jupyter
    'IPython',  # IPython
    'notebook',  # Notebook
    'sphinx',  # 文档生成
    'pytest',  # 测试框架
    'setuptools',  # 安装工具
    'pip',  # 包管理器
]


class Builder:
    """构建器类"""
    
    def __init__(self):
        self.build_dir = Path('build')
        self.dist_dir = Path('dist')
        self.spec_file = Path(f"{BUILD_CONFIG['app_name']}.spec")
        self.system = platform.system()
        
    def print_colored(self, text: str, color: str = 'white') -> None:
        """打印彩色文本"""
        colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'reset': '\033[0m',
        }
        
        color_code = colors.get(color.lower(), colors['white'])
        reset_code = colors['reset']
        print(f"{color_code}{text}{reset_code}")
        
    def check_dependencies(self) -> bool:
        """检查构建依赖"""
        self.print_colored("🔍 检查构建依赖...", 'cyan')
        
        # 检查PyInstaller
        try:
            import PyInstaller
            self.print_colored(f"  ✓ PyInstaller: {PyInstaller.__version__}", 'green')
        except ImportError:
            self.print_colored("  ✗ PyInstaller 未安装", 'red')
            self.print_colored("    请运行: pip install pyinstaller", 'yellow')
            return False
            
        # 检查主脚本
        main_script = Path(BUILD_CONFIG['main_script'])
        if not main_script.exists():
            self.print_colored(f"  ✗ 主脚本不存在: {main_script}", 'red')
            return False
        else:
            self.print_colored(f"  ✓ 主脚本: {main_script}", 'green')
            
        # 检查依赖模块
        missing_modules = []
        for module in ['config', 'utils', 'ide_cleaners', 'registry_cleaner', 'validator']:
            module_file = Path(f"{module}.py")
            if not module_file.exists():
                missing_modules.append(module)
                
        if missing_modules:
            self.print_colored(f"  ✗ 缺少模块: {', '.join(missing_modules)}", 'red')
            return False
        else:
            self.print_colored("  ✓ 所有必需模块都存在", 'green')
            
        return True
        
    def clean_build_dirs(self) -> None:
        """清理构建目录"""
        self.print_colored("🧹 清理构建目录...", 'cyan')
        
        dirs_to_clean = [self.build_dir, self.dist_dir, Path('__pycache__')]
        files_to_clean = [self.spec_file]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.print_colored(f"  ✓ 删除目录: {dir_path}", 'green')
                
        for file_path in files_to_clean:
            if file_path.exists():
                file_path.unlink()
                self.print_colored(f"  ✓ 删除文件: {file_path}", 'green')
                
        # 清理Python缓存文件
        for py_file in Path('.').glob('*.py'):
            cache_file = py_file.with_suffix('.pyc')
            if cache_file.exists():
                cache_file.unlink()
                
    def generate_spec_file(self) -> Path:
        """生成PyInstaller spec文件"""
        self.print_colored("📝 生成spec文件...", 'cyan')
        
        # 构建PyInstaller命令
        cmd = [
            'pyi-makespec',
            '--name', BUILD_CONFIG['app_name'],
        ]
        
        # 添加选项
        if BUILD_CONFIG['onefile']:
            cmd.append('--onefile')
        else:
            cmd.append('--onedir')
            
        if BUILD_CONFIG['console']:
            cmd.append('--console')
        else:
            cmd.append('--windowed')
            
        if BUILD_CONFIG['debug']:
            cmd.append('--debug')
            
        # 添加隐藏导入
        for module in HIDDEN_IMPORTS:
            cmd.extend(['--hidden-import', module])
            
        # 添加排除模块
        for module in EXCLUDES:
            cmd.extend(['--exclude-module', module])
            
        # 添加数据文件
        for src, dst in DATA_FILES:
            cmd.extend(['--add-data', f"{src}{os.pathsep}{dst}"])
            
        # 添加图标
        if BUILD_CONFIG['icon'] and Path(BUILD_CONFIG['icon']).exists():
            cmd.extend(['--icon', BUILD_CONFIG['icon']])
            
        # 添加主脚本
        cmd.append(BUILD_CONFIG['main_script'])
        
        # 执行命令
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                self.print_colored(f"  ✓ spec文件生成成功: {self.spec_file}", 'green')
                return self.spec_file
            else:
                self.print_colored(f"  ✗ spec文件生成失败: {result.stderr}", 'red')
                return None
        except Exception as e:
            self.print_colored(f"  ✗ spec文件生成异常: {e}", 'red')
            return None
            
    def modify_spec_file(self, spec_file: Path) -> None:
        """修改spec文件"""
        self.print_colored("✏️  修改spec文件...", 'cyan')

        try:
            with open(spec_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 简化版本信息处理，避免路径问题
            # 移除可能导致问题的版本资源处理

            self.print_colored("  ✓ spec文件修改完成", 'green')

        except Exception as e:
            self.print_colored(f"  ✗ spec文件修改失败: {e}", 'red')
            
    def build_executable(self, spec_file: Path) -> bool:
        """构建可执行文件"""
        self.print_colored("🔨 构建可执行文件...", 'cyan')
        
        cmd = ['pyinstaller']
        
        # 添加选项
        if BUILD_CONFIG['debug']:
            cmd.append('--debug')
        else:
            cmd.append('--clean')
            
        if BUILD_CONFIG['upx']:
            cmd.append('--upx-dir')
            
        # 添加spec文件
        cmd.append(str(spec_file))
        
        try:
            start_time = time.time()
            
            # 执行构建
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                self.print_colored(f"  ✓ 构建成功! 耗时: {duration:.2f}秒", 'green')
                return True
            else:
                self.print_colored(f"  ✗ 构建失败: {result.stderr}", 'red')
                if BUILD_CONFIG['debug']:
                    self.print_colored(f"详细输出:\n{result.stdout}", 'yellow')
                return False
                
        except Exception as e:
            self.print_colored(f"  ✗ 构建异常: {e}", 'red')
            return False
            
    def post_build_tasks(self) -> None:
        """构建后任务"""
        self.print_colored("📦 执行构建后任务...", 'cyan')
        
        # 查找生成的可执行文件
        exe_name = BUILD_CONFIG['app_name']
        if self.system == 'Windows':
            exe_name += '.exe'
            
        exe_path = self.dist_dir / exe_name
        
        if exe_path.exists():
            # 获取文件信息
            file_size = exe_path.stat().st_size
            size_mb = file_size / 1024 / 1024
            
            self.print_colored(f"  ✓ 可执行文件: {exe_path}", 'green')
            self.print_colored(f"  ✓ 文件大小: {size_mb:.2f} MB", 'green')
            
            # 创建发布目录
            release_dir = Path('release')
            release_dir.mkdir(exist_ok=True)
            
            # 复制可执行文件到发布目录
            release_exe = release_dir / exe_path.name
            shutil.copy2(exe_path, release_exe)
            
            self.print_colored(f"  ✓ 复制到发布目录: {release_exe}", 'green')
            
            # 创建README文件
            readme_content = f"""# {BUILD_CONFIG['app_name']} v{BUILD_CONFIG['version']}

{BUILD_CONFIG['description']}

## 使用方法

### 基本用法
```
{exe_path.name}
```

### 命令行选项
```
{exe_path.name} --help                    # 显示帮助信息
{exe_path.name} --ide jetbrains           # 只清理JetBrains IDE
{exe_path.name} --verbose                 # 详细输出模式
{exe_path.name} --unsafe                  # 不安全模式(更彻底清理)
{exe_path.name} --categories config,cache # 只清理配置和缓存
```

## 系统要求
- Windows 7/8/10/11 (x64)
- 管理员权限 (推荐)

## 构建信息
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 构建平台: {platform.platform()}
- Python版本: {platform.python_version()}
- 文件大小: {size_mb:.2f} MB

## 作者
{BUILD_CONFIG['author']}
"""
            
            readme_file = release_dir / 'README.md'
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
                
            self.print_colored(f"  ✓ 创建README文件: {readme_file}", 'green')
            
        else:
            self.print_colored(f"  ✗ 未找到可执行文件: {exe_path}", 'red')
            
    def build(self) -> bool:
        """执行完整构建流程"""
        self.print_colored("🚀 开始构建 Augment Plugin Cleaner", 'cyan')
        self.print_colored(f"版本: {BUILD_CONFIG['version']}", 'white')
        self.print_colored(f"平台: {self.system}", 'white')
        print()
        
        # 检查依赖
        if not self.check_dependencies():
            return False
            
        # 清理构建目录
        self.clean_build_dirs()
        
        # 生成spec文件
        spec_file = self.generate_spec_file()
        if not spec_file:
            return False
            
        # 修改spec文件
        self.modify_spec_file(spec_file)
        
        # 构建可执行文件
        if not self.build_executable(spec_file):
            return False
            
        # 构建后任务
        self.post_build_tasks()
        
        self.print_colored("\n🎉 构建完成!", 'green')
        return True


def main():
    """主函数"""
    builder = Builder()
    
    try:
        success = builder.build()
        if success:
            print("\n✅ 构建成功! 可执行文件位于 dist/ 和 release/ 目录中")
            sys.exit(0)
        else:
            print("\n❌ 构建失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 构建异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

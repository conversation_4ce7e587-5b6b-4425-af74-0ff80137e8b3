#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Plugin Cleaner
一个用于清理各个IDE中Augment插件缓存和数据的工具

支持的IDE:
- JetBrains系列 (IntelliJ IDEA, PyCharm, WebStorm等)
- Visual Studio Code
- 其他支持Augment的IDE

清理项目:
- 配置文件
- 插件数据和缓存
- 项目数据
- 用户数据
- 聊天记录
- Windows注册表项
"""

import argparse
import os
import sys
import platform
import ctypes
from pathlib import Path
from typing import List, Dict, Optional
import json
import time
from datetime import datetime

# 导入自定义模块
try:
    from config import SUPPORTED_IDES, CLEANUP_CATEGORIES, DEFAULT_PATHS
    from utils import print_colored, confirm_action, is_admin, get_user_home
    from ide_cleaners import IDECleaner
    from registry_cleaner import RegistryCleaner
    from validator import CleanupValidator
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有必要的模块文件都存在")
    sys.exit(1)


class AugmentCleaner:
    """Augment插件清理器主类"""
    
    def __init__(self, verbose: bool = False, unsafe: bool = False):
        self.verbose = verbose
        self.unsafe = unsafe
        self.system = platform.system()
        self.cleanup_stats = {
            'files_deleted': 0,
            'dirs_deleted': 0,
            'registry_keys_deleted': 0,
            'total_size_freed': 0,
            'errors': []
        }
        
        # 初始化清理器
        self.ide_cleaner = IDECleaner(verbose=verbose, unsafe=unsafe)
        self.registry_cleaner = RegistryCleaner(verbose=verbose) if self.system == 'Windows' else None
        self.validator = CleanupValidator(verbose=verbose)
        
    def print_banner(self):
        """打印程序横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Augment Plugin Cleaner                   ║
║                      清理工具 v1.0                          ║
║                                                              ║
║  🧹 清理各个IDE中的Augment插件缓存和数据                    ║
║  🔍 自动验证清理效果                                         ║
║  📊 详细的清理统计报告                                       ║
╚══════════════════════════════════════════════════════════════╝
        """
        print_colored(banner, 'cyan')
        
    def check_permissions(self) -> bool:
        """检查运行权限"""
        if self.system == 'Windows':
            if not is_admin():
                print_colored("⚠️  警告: 建议以管理员身份运行以获得最佳清理效果", 'yellow')
                if not confirm_action("是否继续? (某些文件可能无法删除)"):
                    return False
        else:
            # Linux/macOS权限检查
            if os.geteuid() != 0 and not self.unsafe:
                print_colored("⚠️  警告: 建议使用sudo运行以获得最佳清理效果", 'yellow')
                if not confirm_action("是否继续? (某些文件可能无法删除)"):
                    return False
        return True
        
    def select_ides(self, ide_filter: Optional[List[str]] = None) -> List[str]:
        """选择要清理的IDE"""
        available_ides = list(SUPPORTED_IDES.keys())
        
        if ide_filter:
            # 验证指定的IDE是否支持
            invalid_ides = [ide for ide in ide_filter if ide not in available_ides]
            if invalid_ides:
                print_colored(f"❌ 不支持的IDE: {', '.join(invalid_ides)}", 'red')
                print_colored(f"支持的IDE: {', '.join(available_ides)}", 'cyan')
                return []
            return ide_filter
            
        # 交互式选择
        print_colored("\n📋 选择要清理的IDE:", 'cyan')
        print_colored("0. 全部清理", 'white')
        
        for i, ide in enumerate(available_ides, 1):
            print_colored(f"{i}. {ide}", 'white')
            
        while True:
            try:
                choice = input("\n请输入选择 (0-{}, 多个选择用逗号分隔): ".format(len(available_ides)))
                
                if not choice.strip():
                    continue
                    
                choices = [int(x.strip()) for x in choice.split(',')]
                
                if 0 in choices:
                    return available_ides
                    
                selected_ides = []
                for c in choices:
                    if 1 <= c <= len(available_ides):
                        selected_ides.append(available_ides[c-1])
                    else:
                        raise ValueError(f"无效选择: {c}")
                        
                return selected_ides
                
            except (ValueError, IndexError) as e:
                print_colored(f"❌ 输入错误: {e}", 'red')
                
    def select_cleanup_categories(self, categories_filter: Optional[List[str]] = None) -> List[str]:
        """选择要清理的类别"""
        available_categories = list(CLEANUP_CATEGORIES.keys())
        
        if categories_filter:
            # 验证指定的类别是否支持
            invalid_categories = [cat for cat in categories_filter if cat not in available_categories]
            if invalid_categories:
                print_colored(f"❌ 不支持的清理类别: {', '.join(invalid_categories)}", 'red')
                print_colored(f"支持的类别: {', '.join(available_categories)}", 'cyan')
                return []
            return categories_filter
            
        # 交互式选择
        print_colored("\n🗂️  选择要清理的类别:", 'cyan')
        print_colored("0. 全部清理", 'white')
        
        for i, (category, description) in enumerate(CLEANUP_CATEGORIES.items(), 1):
            print_colored(f"{i}. {category} - {description}", 'white')
            
        while True:
            try:
                choice = input(f"\n请输入选择 (0-{len(available_categories)}, 多个选择用逗号分隔): ")
                
                if not choice.strip():
                    continue
                    
                choices = [int(x.strip()) for x in choice.split(',')]
                
                if 0 in choices:
                    return available_categories
                    
                selected_categories = []
                for c in choices:
                    if 1 <= c <= len(available_categories):
                        selected_categories.append(available_categories[c-1])
                    else:
                        raise ValueError(f"无效选择: {c}")
                        
                return selected_categories
                
            except (ValueError, IndexError) as e:
                print_colored(f"❌ 输入错误: {e}", 'red')
                
    def perform_cleanup(self, selected_ides: List[str], selected_categories: List[str]) -> bool:
        """执行清理操作"""
        print_colored(f"\n🧹 开始清理...", 'green')
        print_colored(f"目标IDE: {', '.join(selected_ides)}", 'white')
        print_colored(f"清理类别: {', '.join(selected_categories)}", 'white')
        
        if not confirm_action("\n⚠️  确认开始清理? 此操作不可撤销!"):
            return False
            
        start_time = time.time()
        
        try:
            # IDE清理
            for ide in selected_ides:
                print_colored(f"\n📱 清理 {ide}...", 'cyan')
                ide_stats = self.ide_cleaner.clean_ide(ide, selected_categories)
                self._merge_stats(ide_stats)
                
            # Windows注册表清理
            if self.system == 'Windows' and 'registry' in selected_categories and self.registry_cleaner:
                print_colored(f"\n🗃️  清理注册表...", 'cyan')
                registry_stats = self.registry_cleaner.clean_registry()
                self._merge_stats(registry_stats)
                
            # 清理完成
            end_time = time.time()
            duration = end_time - start_time
            
            print_colored(f"\n✅ 清理完成! 耗时: {duration:.2f}秒", 'green')
            return True
            
        except Exception as e:
            print_colored(f"\n❌ 清理过程中发生错误: {e}", 'red')
            if self.verbose:
                import traceback
                traceback.print_exc()
            return False
            
    def _merge_stats(self, stats: Dict):
        """合并统计信息"""
        for key in ['files_deleted', 'dirs_deleted', 'registry_keys_deleted', 'total_size_freed']:
            if key in stats:
                self.cleanup_stats[key] += stats[key]
        
        if 'errors' in stats:
            self.cleanup_stats['errors'].extend(stats['errors'])
            
    def generate_report(self) -> str:
        """生成清理报告"""
        report = f"""
╔══════════════════════════════════════════════════════════════╗
║                        清理报告                              ║
╠══════════════════════════════════════════════════════════════╣
║ 清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}                    ║
║ 系统平台: {self.system:<20}                        ║
║                                                              ║
║ 📊 清理统计:                                                 ║
║   • 删除文件: {self.cleanup_stats['files_deleted']:<10} 个                        ║
║   • 删除目录: {self.cleanup_stats['dirs_deleted']:<10} 个                        ║
║   • 删除注册表项: {self.cleanup_stats['registry_keys_deleted']:<10} 个                  ║
║   • 释放空间: {self.cleanup_stats['total_size_freed'] / 1024 / 1024:.2f} MB                    ║
║                                                              ║
"""
        
        if self.cleanup_stats['errors']:
            report += "║ ⚠️  错误信息:                                                ║\n"
            for error in self.cleanup_stats['errors'][:5]:  # 只显示前5个错误
                report += f"║   • {error[:56]:<56} ║\n"
            if len(self.cleanup_stats['errors']) > 5:
                report += f"║   ... 还有 {len(self.cleanup_stats['errors']) - 5} 个错误                                    ║\n"
        
        report += "╚══════════════════════════════════════════════════════════════╝"
        
        return report
        
    def validate_cleanup(self) -> bool:
        """验证清理效果"""
        print_colored("\n🔍 验证清理效果...", 'cyan')
        
        validation_result = self.validator.validate_cleanup()
        
        if validation_result['success']:
            print_colored("✅ 清理验证通过!", 'green')
        else:
            print_colored("❌ 发现残留文件或配置", 'red')
            if self.verbose:
                for issue in validation_result['issues']:
                    print_colored(f"  • {issue}", 'yellow')
                    
        return validation_result['success']


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Augment Plugin Cleaner - 清理各个IDE中的Augment插件缓存和数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python augment_cleaner.py                    # 交互式清理
  python augment_cleaner.py --ide jetbrains    # 只清理JetBrains IDE
  python augment_cleaner.py --verbose          # 详细输出模式
  python augment_cleaner.py --unsafe           # 不安全模式(更彻底清理)
  python augment_cleaner.py --categories config,cache  # 只清理配置和缓存
        """
    )
    
    parser.add_argument('--ide', nargs='+', help='指定要清理的IDE (jetbrains, vscode, 等)')
    parser.add_argument('--categories', help='指定要清理的类别，用逗号分隔')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出模式')
    parser.add_argument('--unsafe', action='store_true', help='不安全模式，进行更彻底的清理')
    parser.add_argument('--no-confirm', action='store_true', help='跳过确认提示')
    parser.add_argument('--report-file', help='将报告保存到指定文件')
    
    args = parser.parse_args()
    
    # 创建清理器实例
    cleaner = AugmentCleaner(verbose=args.verbose, unsafe=args.unsafe)
    
    # 打印横幅
    cleaner.print_banner()
    
    # 检查权限
    if not cleaner.check_permissions():
        sys.exit(1)
        
    # 选择IDE和清理类别
    categories_list = args.categories.split(',') if args.categories else None
    
    selected_ides = cleaner.select_ides(args.ide)
    if not selected_ides:
        sys.exit(1)
        
    selected_categories = cleaner.select_cleanup_categories(categories_list)
    if not selected_categories:
        sys.exit(1)
        
    # 执行清理
    if cleaner.perform_cleanup(selected_ides, selected_categories):
        # 验证清理效果
        cleaner.validate_cleanup()
        
        # 生成并显示报告
        report = cleaner.generate_report()
        print_colored(report, 'cyan')
        
        # 保存报告到文件
        if args.report_file:
            try:
                with open(args.report_file, 'w', encoding='utf-8') as f:
                    f.write(report)
                print_colored(f"\n📄 报告已保存到: {args.report_file}", 'green')
            except Exception as e:
                print_colored(f"❌ 保存报告失败: {e}", 'red')
                
        print_colored("\n🎉 清理完成!", 'green')
    else:
        print_colored("\n❌ 清理失败!", 'red')
        sys.exit(1)


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n\n⚠️  用户中断操作", 'yellow')
        sys.exit(0)
    except Exception as e:
        print_colored(f"\n❌ 程序异常: {e}", 'red')
        sys.exit(1)

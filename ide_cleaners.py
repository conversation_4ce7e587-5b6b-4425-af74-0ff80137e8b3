# -*- coding: utf-8 -*-
"""
IDE清理模块 - 实现对不同IDE的Augment插件数据清理
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
import glob
import re

from config import (
    SUPPORTED_IDES, SYSTEM, PROJECT_AUGMENT_FILES, 
    AUGMENT_FILE_PATTERNS, AUGMENT_DIR_PATTERNS,
    CLEANUP_CONFIG
)
from utils import (
    print_colored, safe_delete_file, safe_delete_directory,
    find_files_by_pattern, get_directory_size, format_size,
    is_process_running, kill_process
)


class IDECleaner:
    """IDE清理器类"""
    
    def __init__(self, verbose: bool = False, unsafe: bool = False):
        self.verbose = verbose
        self.unsafe = unsafe
        self.stats = {
            'files_deleted': 0,
            'dirs_deleted': 0,
            'total_size_freed': 0,
            'errors': []
        }
        
    def clean_ide(self, ide_name: str, categories: List[str]) -> Dict[str, Any]:
        """清理指定IDE的Augment数据"""
        if ide_name not in SUPPORTED_IDES:
            error_msg = f"不支持的IDE: {ide_name}"
            self.stats['errors'].append(error_msg)
            return self.stats
            
        ide_config = SUPPORTED_IDES[ide_name]
        
        if self.verbose:
            print_colored(f"开始清理 {ide_config['name']}...", 'cyan')
            
        # 检查IDE是否正在运行
        if self._check_ide_running(ide_name):
            if not self.unsafe:
                print_colored(f"⚠️  {ide_config['name']} 正在运行，建议先关闭IDE", 'yellow')
                if not self._confirm_continue():
                    return self.stats
            else:
                print_colored(f"🔄 尝试关闭 {ide_config['name']}...", 'yellow')
                self._close_ide(ide_name)
                
        # 执行各类清理
        for category in categories:
            if self.verbose:
                print_colored(f"  清理类别: {category}", 'white')
                
            if category == 'config':
                self._clean_config_files(ide_name, ide_config)
            elif category == 'plugins':
                self._clean_plugin_data(ide_name, ide_config)
            elif category == 'projects':
                self._clean_project_data(ide_name)
            elif category == 'cache':
                self._clean_cache_files(ide_name, ide_config)
            elif category == 'userdata':
                self._clean_user_data(ide_name)
            elif category == 'chats':
                self._clean_chat_data(ide_name)
                
        if self.verbose:
            print_colored(f"✅ {ide_config['name']} 清理完成", 'green')
            
        return self.stats.copy()
        
    def _check_ide_running(self, ide_name: str) -> bool:
        """检查IDE是否正在运行"""
        process_names = {
            'jetbrains': ['idea64.exe', 'pycharm64.exe', 'webstorm64.exe', 'phpstorm64.exe', 
                         'idea', 'pycharm', 'webstorm', 'phpstorm'],
            'vscode': ['Code.exe', 'code'],
            'cursor': ['Cursor.exe', 'cursor']
        }
        
        if ide_name not in process_names:
            return False
            
        for process_name in process_names[ide_name]:
            if is_process_running(process_name):
                return True
                
        return False
        
    def _close_ide(self, ide_name: str) -> bool:
        """尝试关闭IDE"""
        process_names = {
            'jetbrains': ['idea64.exe', 'pycharm64.exe', 'webstorm64.exe', 'phpstorm64.exe'],
            'vscode': ['Code.exe'],
            'cursor': ['Cursor.exe']
        }
        
        if ide_name not in process_names:
            return False
            
        success = True
        for process_name in process_names[ide_name]:
            if is_process_running(process_name):
                if not kill_process(process_name):
                    success = False
                    
        return success
        
    def _confirm_continue(self) -> bool:
        """确认是否继续"""
        from utils import confirm_action
        return confirm_action("是否继续清理?")
        
    def _clean_config_files(self, ide_name: str, ide_config: Dict) -> None:
        """清理配置文件"""
        config_paths = ide_config.get('config_paths', {}).get(SYSTEM, [])
        
        for base_path in config_paths:
            if not isinstance(base_path, Path):
                base_path = Path(base_path)
                
            if not base_path.exists():
                continue
                
            # 查找Augment相关配置文件
            augment_configs = []
            
            # 在options目录中查找
            options_dir = base_path / 'options'
            if options_dir.exists():
                for pattern in AUGMENT_FILE_PATTERNS:
                    augment_configs.extend(options_dir.glob(pattern))
                    
            # 在配置根目录中查找
            for pattern in AUGMENT_FILE_PATTERNS:
                augment_configs.extend(base_path.glob(pattern))
                
            # 删除找到的配置文件
            for config_file in augment_configs:
                self._delete_path(config_file, "配置文件")
                
    def _clean_plugin_data(self, ide_name: str, ide_config: Dict) -> None:
        """清理插件数据"""
        plugin_paths = ide_config.get('plugin_paths', {}).get(SYSTEM, [])
        
        for plugin_path_pattern in plugin_paths:
            if not isinstance(plugin_path_pattern, Path):
                plugin_path_pattern = Path(plugin_path_pattern)
                
            # 处理通配符路径
            parent_path = plugin_path_pattern.parent
            pattern = plugin_path_pattern.name
            
            if '*' in str(parent_path):
                # 父路径也包含通配符
                base_parts = str(plugin_path_pattern).split('*')
                if len(base_parts) >= 2:
                    base_path = Path(base_parts[0]).parent
                    if base_path.exists():
                        for item in base_path.rglob('*augment*'):
                            if item.is_dir() and 'plugin' in str(item).lower():
                                self._delete_path(item, "插件目录")
            else:
                # 只有文件名包含通配符
                if parent_path.exists():
                    for item in parent_path.glob(pattern):
                        self._delete_path(item, "插件数据")
                        
    def _clean_project_data(self, ide_name: str) -> None:
        """清理项目数据"""
        # 查找常见的项目目录
        search_paths = []
        
        if SYSTEM == 'Windows':
            # Windows常见项目路径
            search_paths.extend([
                Path.home() / 'Documents',
                Path.home() / 'Desktop',
                Path('C:') / 'Projects',
                Path('D:') / 'Projects',
            ])
        else:
            # Linux/macOS常见项目路径
            search_paths.extend([
                Path.home() / 'Projects',
                Path.home() / 'Documents',
                Path.home() / 'Desktop',
                Path.home() / 'workspace',
                Path.home() / 'dev',
            ])
            
        # 查找项目中的Augment文件
        for search_path in search_paths:
            if not search_path.exists():
                continue
                
            try:
                # 限制搜索深度以避免性能问题
                for root, dirs, files in os.walk(search_path):
                    # 限制搜索深度为3层
                    level = root.replace(str(search_path), '').count(os.sep)
                    if level >= 3:
                        dirs[:] = []  # 不再深入子目录
                        continue
                        
                    # 检查是否是项目目录（包含常见的项目文件）
                    project_indicators = ['.git', '.svn', 'package.json', 'pom.xml', 
                                        'Cargo.toml', 'requirements.txt', '.project']
                    
                    is_project = any(
                        os.path.exists(os.path.join(root, indicator)) 
                        for indicator in project_indicators
                    )
                    
                    if is_project:
                        # 在项目目录中查找Augment文件
                        for augment_file in PROJECT_AUGMENT_FILES:
                            file_path = Path(root) / augment_file
                            if file_path.exists():
                                self._delete_path(file_path, "项目数据")
                                
                        # 查找.IDE目录中的Augment文件
                        ide_dirs = [d for d in dirs if d.startswith('.') and 'ide' in d.lower()]
                        for ide_dir in ide_dirs:
                            ide_path = Path(root) / ide_dir
                            for pattern in AUGMENT_FILE_PATTERNS:
                                for augment_file in ide_path.glob(pattern):
                                    self._delete_path(augment_file, "IDE项目数据")
                                    
            except (PermissionError, OSError) as e:
                if self.verbose:
                    print_colored(f"  跳过路径 {search_path}: {e}", 'yellow')
                    
    def _clean_cache_files(self, ide_name: str, ide_config: Dict) -> None:
        """清理缓存文件"""
        # 清理IDE特定的缓存路径
        config_paths = ide_config.get('config_paths', {}).get(SYSTEM, [])
        
        for base_path in config_paths:
            if not isinstance(base_path, Path):
                base_path = Path(base_path)
                
            if not base_path.exists():
                continue
                
            # 查找缓存目录
            cache_dirs = ['cache', 'caches', 'Cache', 'Caches', 'temp', 'tmp']
            
            for cache_dir_name in cache_dirs:
                cache_dir = base_path / cache_dir_name
                if cache_dir.exists():
                    # 在缓存目录中查找Augment相关文件
                    for pattern in AUGMENT_FILE_PATTERNS + AUGMENT_DIR_PATTERNS:
                        for item in cache_dir.glob(pattern):
                            self._delete_path(item, "缓存文件")
                            
        # 清理系统临时目录中的Augment文件
        temp_dirs = []
        if SYSTEM == 'Windows':
            temp_dirs.extend([
                Path(os.environ.get('TEMP', '')),
                Path(os.environ.get('TMP', '')),
                Path(os.environ.get('LOCALAPPDATA', '')) / 'Temp',
            ])
        else:
            temp_dirs.extend([
                Path('/tmp'),
                Path('/var/tmp'),
                Path.home() / '.cache',
            ])
            
        for temp_dir in temp_dirs:
            if temp_dir.exists():
                for pattern in AUGMENT_FILE_PATTERNS + AUGMENT_DIR_PATTERNS:
                    try:
                        for item in temp_dir.glob(pattern):
                            self._delete_path(item, "临时文件")
                    except (PermissionError, OSError):
                        pass  # 忽略权限错误
                        
    def _clean_user_data(self, ide_name: str) -> None:
        """清理用户数据"""
        from config import AUGMENT_PATHS
        
        user_paths = AUGMENT_PATHS.get(SYSTEM, [])
        
        for user_path in user_paths:
            if not isinstance(user_path, Path):
                user_path = Path(user_path)
                
            # 处理通配符路径
            if '*' in str(user_path):
                parent_path = user_path.parent
                pattern = user_path.name
                
                if parent_path.exists():
                    for item in parent_path.glob(pattern):
                        self._delete_path(item, "用户数据")
            else:
                if user_path.exists():
                    self._delete_path(user_path, "用户数据")
                    
        # 清理.jetbrains目录
        jetbrains_dir = Path.home() / '.jetbrains'
        if jetbrains_dir.exists():
            for pattern in AUGMENT_FILE_PATTERNS + AUGMENT_DIR_PATTERNS:
                for item in jetbrains_dir.rglob(pattern):
                    self._delete_path(item, "JetBrains用户数据")
                    
    def _clean_chat_data(self, ide_name: str) -> None:
        """清理聊天数据"""
        from config import CHAT_PATHS
        
        chat_paths = CHAT_PATHS.get(SYSTEM, [])
        
        for chat_path in chat_paths:
            if not isinstance(chat_path, Path):
                chat_path = Path(chat_path)
                
            if chat_path.exists():
                self._delete_path(chat_path, "聊天记录")
                
    def _delete_path(self, path: Path, category: str) -> None:
        """删除路径（文件或目录）"""
        if not path.exists():
            return
            
        try:
            if path.is_file():
                result = safe_delete_file(path, backup=CLEANUP_CONFIG['backup_before_delete'])
                if result['success']:
                    self.stats['files_deleted'] += 1
                    self.stats['total_size_freed'] += result['size']
                    if self.verbose:
                        print_colored(f"    ✓ 删除{category}: {path}", 'green')
                else:
                    error_msg = f"删除{category}失败: {path} - {result['error']}"
                    self.stats['errors'].append(error_msg)
                    if self.verbose:
                        print_colored(f"    ✗ {error_msg}", 'red')
                        
            elif path.is_dir():
                result = safe_delete_directory(path, backup=CLEANUP_CONFIG['backup_before_delete'])
                if result['success']:
                    self.stats['dirs_deleted'] += 1
                    self.stats['files_deleted'] += result['files_deleted']
                    self.stats['total_size_freed'] += result['size_freed']
                    if self.verbose:
                        print_colored(f"    ✓ 删除{category}目录: {path} ({result['files_deleted']}个文件)", 'green')
                else:
                    error_msg = f"删除{category}目录失败: {path} - {result['error']}"
                    self.stats['errors'].append(error_msg)
                    if self.verbose:
                        print_colored(f"    ✗ {error_msg}", 'red')
                        
        except Exception as e:
            error_msg = f"删除{category}异常: {path} - {e}"
            self.stats['errors'].append(error_msg)
            if self.verbose:
                print_colored(f"    ✗ {error_msg}", 'red')

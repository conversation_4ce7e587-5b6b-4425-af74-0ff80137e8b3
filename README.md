# Augment Plugin Cleaner

一个用于清理各个IDE中Augment插件缓存和数据的强大工具。

## 🌟 功能特性

### 支持的IDE
- **JetBrains系列**: IntelliJ IDEA, PyCharm, WebStorm, PhpStorm等
- **Visual Studio Code**: VS Code编辑器
- **Cursor Editor**: Cursor AI编辑器
- **其他**: 支持扩展到更多IDE

### 清理项目
- ✅ **配置文件**: options目录中的Augment相关配置
- ✅ **插件数据**: 插件目录中的缓存和数据文件
- ✅ **项目数据**: .IDE目录中的Augment文件
- ✅ **缓存文件**: 各种临时和缓存文件
- ✅ **用户数据**: .jetbrains和.augmentcode目录
- ✅ **聊天记录**: Augment聊天历史记录
- ✅ **注册表项**: Windows注册表中的相关项 (仅Windows)

### 清理效果验证
- 🔍 **自动验证**: 程序内置验证功能，自动检查清理效果
- 📊 **残留文件扫描**: 扫描并报告残留文件和目录
- 📋 **配置重置确认**: 确认配置文件已正确清理
- 📈 **清理统计报告**: 详细的清理统计和操作报告
- ✅ **操作完整性检查**: 验证所有清理操作的完整性

## 🚀 快速开始

### 方法一：使用预编译的exe文件
1. 下载最新的 `augment-cleaner.exe`
2. 右键"以管理员身份运行" (推荐)
3. 按照提示选择要清理的IDE和项目

### 方法二：从源码运行
```bash
# 克隆项目
git clone <repository-url>
cd augment-cleaner

# 安装依赖
pip install -r requirements.txt

# 运行程序
python augment_cleaner.py
```

## 📖 使用方法

### 交互式清理
```bash
# 运行程序，按提示操作
augment-cleaner.exe
```

### 命令行选项
```bash
# 显示帮助信息
augment-cleaner.exe --help

# 只清理JetBrains IDE
augment-cleaner.exe --ide jetbrains

# 只清理VS Code
augment-cleaner.exe --ide vscode

# 清理多个IDE
augment-cleaner.exe --ide jetbrains vscode

# 只清理特定类别
augment-cleaner.exe --categories config,cache,plugins

# 详细输出模式
augment-cleaner.exe --verbose

# 不安全模式(更彻底清理)
augment-cleaner.exe --unsafe

# 跳过确认提示
augment-cleaner.exe --no-confirm

# 保存报告到文件
augment-cleaner.exe --report-file cleanup_report.txt
```

### 使用示例
```bash
# 完整清理所有IDE
augment-cleaner.exe --verbose

# 只清理JetBrains的配置和缓存
augment-cleaner.exe --ide jetbrains --categories config,cache

# 彻底清理(包括可能有风险的文件)
augment-cleaner.exe --unsafe --verbose
```

## 🛠️ 开发和构建

### 环境要求
- Python 3.7+
- Windows 7/8/10/11 (推荐)
- Linux/macOS (部分功能)

### 安装开发依赖
```bash
pip install -r requirements.txt
```

### 构建exe文件
```bash
# 运行构建脚本
python build.py

# 生成的文件位于 dist/ 目录
```

### 项目结构
```
augment-cleaner/
├── augment_cleaner.py      # 主程序
├── config.py               # 配置文件
├── utils.py                # 工具函数
├── ide_cleaners.py         # IDE清理模块
├── registry_cleaner.py     # 注册表清理模块
├── validator.py            # 验证和报告模块
├── build.py                # 构建脚本
├── requirements.txt        # 依赖列表
└── README.md              # 说明文档
```

## ⚠️ 注意事项

### 权限要求
- **Windows**: 建议以管理员身份运行以获得最佳清理效果
- **Linux/macOS**: 建议使用sudo运行

### 安全提示
- 程序会在删除前自动备份重要文件
- 使用 `--unsafe` 模式时请谨慎，可能删除重要数据
- 建议在清理前关闭所有相关IDE

### 兼容性
- 主要针对Windows平台优化
- Linux和macOS支持基本功能
- 注册表清理仅在Windows上可用

## 🔧 故障排除

### 常见问题

#### 📁 清理不完整
❌ **问题**: 仍有残留文件
✅ **解决方案**:
1. 使用 `--verbose` 模式查看详细信息
2. 尝试 `--unsafe` 模式进行更彻底清理
3. 手动检查和删除残留文件
4. 重新运行清理程序

#### 🔒 权限问题
❌ **问题**: 无法修改hosts文件或删除某些文件
✅ **解决方案**:
- **Windows**: 右键"以管理员身份运行"
- **Linux/macOS**: 使用 `sudo ./augment-cleaner`
- 检查文件是否被其他程序占用

#### 🚫 IDE仍在运行
❌ **问题**: 清理时IDE正在运行
✅ **解决方案**:
1. 关闭所有相关IDE
2. 使用 `--unsafe` 模式强制清理
3. 重启计算机后再次尝试

## 📊 清理报告示例

```
╔══════════════════════════════════════════════════════════════╗
║                        清理报告                              ║
╠══════════════════════════════════════════════════════════════╣
║ 清理时间: 2024-01-15 14:30:25                               ║
║ 系统平台: Windows                                            ║
║                                                              ║
║ 📊 清理统计:                                                 ║
║   • 删除文件: 156        个                                  ║
║   • 删除目录: 23         个                                  ║
║   • 删除注册表项: 8      个                                  ║
║   • 释放空间: 245.67 MB                                      ║
║                                                              ║
║ ✅ 验证状态: 通过                                            ║
╚══════════════════════════════════════════════════════════════╝
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

### 贡献指南
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢所有贡献者的努力
- 感谢开源社区的支持
- 特别感谢PyInstaller项目

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**⚡ 让你的IDE重新焕发活力！**

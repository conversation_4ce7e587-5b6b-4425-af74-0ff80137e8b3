# -*- coding: utf-8 -*-
"""
验证和报告模块 - 实现清理效果验证、残留文件扫描和清理统计报告
"""

import os
import platform
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import time
import json

# Windows注册表操作
if platform.system() == 'Windows':
    try:
        import winreg
    except ImportError:
        winreg = None
else:
    winreg = None

from config import (
    SUPPORTED_IDES, SYSTEM, AUGMENT_PATHS, CHAT_PATHS,
    AUGMENT_FILE_PATTERNS, AUGMENT_DIR_PATTERNS,
    REGISTRY_PATHS, IS_WINDOWS, VALIDATION_CONFIG
)
from utils import (
    print_colored, find_files_by_pattern, get_directory_size,
    format_size, is_process_running
)


class CleanupValidator:
    """清理效果验证器"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.validation_results = {
            'success': True,
            'issues': [],
            'residual_files': [],
            'residual_registry_keys': [],
            'running_processes': [],
            'scan_summary': {
                'files_found': 0,
                'dirs_found': 0,
                'registry_keys_found': 0,
                'total_size': 0
            }
        }
        
    def validate_cleanup(self) -> Dict[str, Any]:
        """验证清理效果"""
        if self.verbose:
            print_colored("🔍 开始验证清理效果...", 'cyan')
            
        # 扫描残留文件
        if VALIDATION_CONFIG['check_residual_files']:
            self._scan_residual_files()
            
        # 检查注册表残留
        if VALIDATION_CONFIG['check_registry_entries'] and IS_WINDOWS and winreg:
            self._scan_residual_registry()
            
        # 检查运行中的进程
        if VALIDATION_CONFIG['check_running_processes']:
            self._check_running_processes()
            
        # 生成验证报告
        self._generate_validation_report()
        
        # 判断验证是否成功
        self.validation_results['success'] = (
            len(self.validation_results['residual_files']) == 0 and
            len(self.validation_results['residual_registry_keys']) == 0 and
            len(self.validation_results['running_processes']) == 0
        )
        
        return self.validation_results.copy()
        
    def _scan_residual_files(self) -> None:
        """扫描残留文件"""
        if self.verbose:
            print_colored("  📁 扫描残留文件...", 'white')
            
        scan_paths = []
        
        # 添加IDE配置路径
        for ide_name, ide_config in SUPPORTED_IDES.items():
            config_paths = ide_config.get('config_paths', {}).get(SYSTEM, [])
            scan_paths.extend(config_paths)
            
            plugin_paths = ide_config.get('plugin_paths', {}).get(SYSTEM, [])
            scan_paths.extend(plugin_paths)
            
        # 添加Augment特定路径
        augment_paths = AUGMENT_PATHS.get(SYSTEM, [])
        scan_paths.extend(augment_paths)
        
        # 添加聊天记录路径
        chat_paths = CHAT_PATHS.get(SYSTEM, [])
        scan_paths.extend(chat_paths)
        
        # 扫描每个路径
        for scan_path in scan_paths:
            if not isinstance(scan_path, Path):
                scan_path = Path(scan_path)
                
            self._scan_path_for_residuals(scan_path)
            
        # 扫描常见的临时目录
        self._scan_temp_directories()
        
        if self.verbose:
            files_count = self.validation_results['scan_summary']['files_found']
            dirs_count = self.validation_results['scan_summary']['dirs_found']
            total_size = format_size(self.validation_results['scan_summary']['total_size'])
            
            if files_count > 0 or dirs_count > 0:
                print_colored(f"    ⚠️  发现残留: {files_count}个文件, {dirs_count}个目录, 总大小: {total_size}", 'yellow')
            else:
                print_colored("    ✅ 未发现残留文件", 'green')
                
    def _scan_path_for_residuals(self, base_path: Path) -> None:
        """扫描指定路径的残留文件"""
        if not base_path.exists():
            return
            
        try:
            # 处理通配符路径
            if '*' in str(base_path):
                parent_path = base_path.parent
                pattern = base_path.name
                
                if parent_path.exists():
                    for item in parent_path.glob(pattern):
                        self._check_item_for_augment(item)
            else:
                # 在路径中查找Augment相关文件
                for pattern in AUGMENT_FILE_PATTERNS + AUGMENT_DIR_PATTERNS:
                    try:
                        for item in base_path.rglob(pattern):
                            self._check_item_for_augment(item)
                    except (PermissionError, OSError):
                        pass  # 忽略权限错误
                        
        except (PermissionError, OSError) as e:
            if self.verbose:
                print_colored(f"    跳过路径 {base_path}: {e}", 'yellow')
                
    def _check_item_for_augment(self, item: Path) -> None:
        """检查项目是否与Augment相关"""
        item_name_lower = item.name.lower()
        augment_keywords = ['augment', 'augmentcode']
        
        # 检查是否包含Augment关键词
        if any(keyword in item_name_lower for keyword in augment_keywords):
            try:
                if item.is_file():
                    size = item.stat().st_size
                    self.validation_results['residual_files'].append({
                        'path': str(item),
                        'type': 'file',
                        'size': size
                    })
                    self.validation_results['scan_summary']['files_found'] += 1
                    self.validation_results['scan_summary']['total_size'] += size
                    
                elif item.is_dir():
                    size = get_directory_size(item)
                    self.validation_results['residual_files'].append({
                        'path': str(item),
                        'type': 'directory',
                        'size': size
                    })
                    self.validation_results['scan_summary']['dirs_found'] += 1
                    self.validation_results['scan_summary']['total_size'] += size
                    
            except (PermissionError, OSError):
                pass  # 忽略权限错误
                
    def _scan_temp_directories(self) -> None:
        """扫描临时目录"""
        temp_dirs = []
        
        if IS_WINDOWS:
            temp_dirs.extend([
                Path(os.environ.get('TEMP', '')),
                Path(os.environ.get('TMP', '')),
                Path(os.environ.get('LOCALAPPDATA', '')) / 'Temp',
            ])
        else:
            temp_dirs.extend([
                Path('/tmp'),
                Path('/var/tmp'),
                Path.home() / '.cache',
            ])
            
        for temp_dir in temp_dirs:
            if temp_dir.exists():
                for pattern in AUGMENT_FILE_PATTERNS + AUGMENT_DIR_PATTERNS:
                    try:
                        for item in temp_dir.glob(pattern):
                            self._check_item_for_augment(item)
                    except (PermissionError, OSError):
                        pass
                        
    def _scan_residual_registry(self) -> None:
        """扫描残留的注册表项"""
        if not IS_WINDOWS or winreg is None:
            return
            
        if self.verbose:
            print_colored("  🗃️  扫描残留注册表项...", 'white')
            
        for registry_path in REGISTRY_PATHS:
            try:
                hive, subkey = self._parse_registry_path(registry_path)
                if hive is None:
                    continue
                    
                # 查找匹配的键
                matching_keys = self._find_matching_registry_keys(hive, subkey)
                
                for key_path in matching_keys:
                    self.validation_results['residual_registry_keys'].append({
                        'hive': self._hive_to_string(hive),
                        'path': key_path
                    })
                    self.validation_results['scan_summary']['registry_keys_found'] += 1
                    
            except Exception as e:
                if self.verbose:
                    print_colored(f"    扫描注册表失败: {registry_path} - {e}", 'yellow')
                    
        if self.verbose:
            keys_count = self.validation_results['scan_summary']['registry_keys_found']
            if keys_count > 0:
                print_colored(f"    ⚠️  发现残留注册表项: {keys_count}个", 'yellow')
            else:
                print_colored("    ✅ 未发现残留注册表项", 'green')
                
    def _parse_registry_path(self, registry_path: str) -> Tuple[Optional[int], str]:
        """解析注册表路径"""
        parts = registry_path.split('\\', 1)
        if len(parts) != 2:
            return None, ""
            
        hive_name, subkey = parts
        
        hive_map = {
            'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER,
            'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE,
            'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT,
            'HKEY_USERS': winreg.HKEY_USERS,
            'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG,
        }
        
        return hive_map.get(hive_name), subkey
        
    def _hive_to_string(self, hive: int) -> str:
        """将注册表hive转换为字符串"""
        hive_map = {
            winreg.HKEY_CURRENT_USER: 'HKEY_CURRENT_USER',
            winreg.HKEY_LOCAL_MACHINE: 'HKEY_LOCAL_MACHINE',
            winreg.HKEY_CLASSES_ROOT: 'HKEY_CLASSES_ROOT',
            winreg.HKEY_USERS: 'HKEY_USERS',
            winreg.HKEY_CURRENT_CONFIG: 'HKEY_CURRENT_CONFIG',
        }
        return hive_map.get(hive, 'UNKNOWN')
        
    def _find_matching_registry_keys(self, hive: int, subkey_pattern: str) -> List[str]:
        """查找匹配的注册表键"""
        matching_keys = []
        
        try:
            if '*' in subkey_pattern:
                # 处理通配符
                base_parts = subkey_pattern.split('*')
                if len(base_parts) >= 2:
                    base_path = base_parts[0].rstrip('\\')
                    
                    try:
                        with winreg.OpenKey(hive, base_path) as base_key:
                            i = 0
                            while True:
                                try:
                                    subkey_name = winreg.EnumKey(base_key, i)
                                    if self._is_augment_related(subkey_name):
                                        full_path = f"{base_path}\\{subkey_name}"
                                        matching_keys.append(full_path)
                                    i += 1
                                except OSError:
                                    break
                    except FileNotFoundError:
                        pass
            else:
                # 直接检查键是否存在
                try:
                    with winreg.OpenKey(hive, subkey_pattern):
                        matching_keys.append(subkey_pattern)
                except FileNotFoundError:
                    pass
                    
        except Exception:
            pass
            
        return matching_keys
        
    def _is_augment_related(self, key_name: str) -> bool:
        """检查键名是否与Augment相关"""
        key_name_lower = key_name.lower()
        augment_keywords = ['augment', 'augmentcode']
        
        return any(keyword in key_name_lower for keyword in augment_keywords)
        
    def _check_running_processes(self) -> None:
        """检查运行中的Augment相关进程"""
        if self.verbose:
            print_colored("  🔄 检查运行中的进程...", 'white')
            
        augment_processes = [
            'augment', 'augmentcode', 'augment.exe', 'augmentcode.exe'
        ]
        
        for process_name in augment_processes:
            if is_process_running(process_name):
                self.validation_results['running_processes'].append(process_name)
                
        if self.verbose:
            process_count = len(self.validation_results['running_processes'])
            if process_count > 0:
                print_colored(f"    ⚠️  发现运行中的Augment进程: {process_count}个", 'yellow')
            else:
                print_colored("    ✅ 未发现运行中的Augment进程", 'green')
                
    def _generate_validation_report(self) -> None:
        """生成验证报告"""
        issues = []
        
        # 检查残留文件
        if self.validation_results['residual_files']:
            issues.append(f"发现 {len(self.validation_results['residual_files'])} 个残留文件/目录")
            
        # 检查残留注册表项
        if self.validation_results['residual_registry_keys']:
            issues.append(f"发现 {len(self.validation_results['residual_registry_keys'])} 个残留注册表项")
            
        # 检查运行中的进程
        if self.validation_results['running_processes']:
            issues.append(f"发现 {len(self.validation_results['running_processes'])} 个运行中的Augment进程")
            
        self.validation_results['issues'] = issues
        
    def get_detailed_report(self) -> str:
        """获取详细的验证报告"""
        report = "\n" + "="*60 + "\n"
        report += "                    清理验证报告\n"
        report += "="*60 + "\n"
        
        # 总体状态
        if self.validation_results['success']:
            report += "✅ 验证状态: 通过\n"
        else:
            report += "❌ 验证状态: 发现问题\n"
            
        report += f"验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 扫描统计
        summary = self.validation_results['scan_summary']
        report += "📊 扫描统计:\n"
        report += f"  • 残留文件: {summary['files_found']} 个\n"
        report += f"  • 残留目录: {summary['dirs_found']} 个\n"
        report += f"  • 残留注册表项: {summary['registry_keys_found']} 个\n"
        report += f"  • 总大小: {format_size(summary['total_size'])}\n\n"
        
        # 详细问题列表
        if self.validation_results['issues']:
            report += "⚠️  发现的问题:\n"
            for issue in self.validation_results['issues']:
                report += f"  • {issue}\n"
            report += "\n"
            
        # 残留文件详情
        if self.validation_results['residual_files']:
            report += "📁 残留文件详情:\n"
            for item in self.validation_results['residual_files'][:10]:  # 只显示前10个
                size_str = format_size(item['size'])
                report += f"  • [{item['type']}] {item['path']} ({size_str})\n"
            
            if len(self.validation_results['residual_files']) > 10:
                remaining = len(self.validation_results['residual_files']) - 10
                report += f"  ... 还有 {remaining} 个文件/目录\n"
            report += "\n"
            
        # 残留注册表项详情
        if self.validation_results['residual_registry_keys']:
            report += "🗃️  残留注册表项详情:\n"
            for item in self.validation_results['residual_registry_keys'][:10]:
                report += f"  • {item['hive']}\\{item['path']}\n"
                
            if len(self.validation_results['residual_registry_keys']) > 10:
                remaining = len(self.validation_results['residual_registry_keys']) - 10
                report += f"  ... 还有 {remaining} 个注册表项\n"
            report += "\n"
            
        # 运行中的进程
        if self.validation_results['running_processes']:
            report += "🔄 运行中的进程:\n"
            for process in self.validation_results['running_processes']:
                report += f"  • {process}\n"
            report += "\n"
            
        # 建议
        if not self.validation_results['success']:
            report += "💡 建议:\n"
            report += "  1. 使用 --verbose 模式查看详细信息\n"
            report += "  2. 尝试 --unsafe 模式进行更彻底清理\n"
            report += "  3. 手动检查和删除残留文件\n"
            report += "  4. 重新运行清理程序\n"
            
        report += "="*60
        
        return report

# -*- coding: utf-8 -*-
"""
配置文件 - 定义所有路径、常量和配置信息
"""

import os
import platform
from pathlib import Path

# 系统信息
SYSTEM = platform.system()
IS_WINDOWS = SYSTEM == 'Windows'
IS_LINUX = SYSTEM == 'Linux'
IS_MACOS = SYSTEM == 'Darwin'

# 用户目录
if IS_WINDOWS:
    USER_HOME = Path(os.environ.get('USERPROFILE', ''))
    APPDATA = Path(os.environ.get('APPDATA', ''))
    LOCALAPPDATA = Path(os.environ.get('LOCALAPPDATA', ''))
else:
    USER_HOME = Path.home()
    APPDATA = USER_HOME
    LOCALAPPDATA = USER_HOME

# 支持的IDE配置
SUPPORTED_IDES = {
    'jetbrains': {
        'name': 'JetBrains IDEs',
        'description': 'IntelliJ IDEA, PyCharm, WebStorm, PhpStorm等',
        'config_paths': {
            'Windows': [
                APPDATA / 'JetBrains',
                LOCALAPPDATA / 'JetBrains',
            ],
            'Linux': [
                USER_HOME / '.config' / 'JetBrains',
                USER_HOME / '.local' / 'share' / 'JetBrains',
                USER_HOME / '.cache' / 'JetBrains',
            ],
            'Darwin': [
                USER_HOME / 'Library' / 'Application Support' / 'JetBrains',
                USER_HOME / 'Library' / 'Caches' / 'JetBrains',
                USER_HOME / 'Library' / 'Logs' / 'JetBrains',
            ]
        },
        'plugin_paths': {
            'Windows': [
                APPDATA / 'JetBrains' / '*' / 'plugins' / 'augment*',
                LOCALAPPDATA / 'JetBrains' / '*' / 'plugins' / 'augment*',
            ],
            'Linux': [
                USER_HOME / '.local' / 'share' / 'JetBrains' / '*' / 'plugins' / 'augment*',
            ],
            'Darwin': [
                USER_HOME / 'Library' / 'Application Support' / 'JetBrains' / '*' / 'plugins' / 'augment*',
            ]
        }
    },
    
    'vscode': {
        'name': 'Visual Studio Code',
        'description': 'VS Code编辑器',
        'config_paths': {
            'Windows': [
                APPDATA / 'Code',
                LOCALAPPDATA / 'Programs' / 'Microsoft VS Code',
            ],
            'Linux': [
                USER_HOME / '.config' / 'Code',
                USER_HOME / '.vscode',
            ],
            'Darwin': [
                USER_HOME / 'Library' / 'Application Support' / 'Code',
            ]
        },
        'plugin_paths': {
            'Windows': [
                USER_HOME / '.vscode' / 'extensions' / '*augment*',
            ],
            'Linux': [
                USER_HOME / '.vscode' / 'extensions' / '*augment*',
            ],
            'Darwin': [
                USER_HOME / '.vscode' / 'extensions' / '*augment*',
            ]
        }
    },
    
    'cursor': {
        'name': 'Cursor Editor',
        'description': 'Cursor AI编辑器',
        'config_paths': {
            'Windows': [
                APPDATA / 'Cursor',
                LOCALAPPDATA / 'Programs' / 'cursor',
            ],
            'Linux': [
                USER_HOME / '.config' / 'Cursor',
            ],
            'Darwin': [
                USER_HOME / 'Library' / 'Application Support' / 'Cursor',
            ]
        },
        'plugin_paths': {
            'Windows': [
                USER_HOME / '.cursor' / 'extensions' / '*augment*',
            ],
            'Linux': [
                USER_HOME / '.cursor' / 'extensions' / '*augment*',
            ],
            'Darwin': [
                USER_HOME / '.cursor' / 'extensions' / '*augment*',
            ]
        }
    }
}

# 清理类别配置
CLEANUP_CATEGORIES = {
    'config': '配置文件 - options目录中的Augment相关配置',
    'plugins': '插件数据 - 插件目录中的缓存和数据文件',
    'projects': '项目数据 - .IDE目录中的Augment文件',
    'cache': '缓存文件 - 各种临时和缓存文件',
    'userdata': '用户数据 - .jetbrains和.augmentcode目录',
    'chats': '聊天记录 - Augment聊天历史记录',
    'registry': '注册表项 - Windows注册表中的相关项 (仅Windows)',
}

# Augment相关路径
AUGMENT_PATHS = {
    'Windows': [
        USER_HOME / '.augmentcode',
        APPDATA / 'augmentcode',
        LOCALAPPDATA / 'augmentcode',
        LOCALAPPDATA / 'Temp' / 'augment*',
    ],
    'Linux': [
        USER_HOME / '.augmentcode',
        USER_HOME / '.config' / 'augmentcode',
        USER_HOME / '.cache' / 'augmentcode',
        Path('/tmp') / 'augment*',
    ],
    'Darwin': [
        USER_HOME / '.augmentcode',
        USER_HOME / 'Library' / 'Application Support' / 'augmentcode',
        USER_HOME / 'Library' / 'Caches' / 'augmentcode',
        Path('/tmp') / 'augment*',
    ]
}

# 项目级Augment文件
PROJECT_AUGMENT_FILES = [
    '.augment',
    '.augmentcode',
    'augment.json',
    'augment.config.json',
    '.augment.cache',
]

# 聊天记录路径
CHAT_PATHS = {
    'Windows': [
        APPDATA / 'augmentcode' / 'chats',
        LOCALAPPDATA / 'augmentcode' / 'chats',
        USER_HOME / '.augmentcode' / 'chats',
    ],
    'Linux': [
        USER_HOME / '.augmentcode' / 'chats',
        USER_HOME / '.config' / 'augmentcode' / 'chats',
    ],
    'Darwin': [
        USER_HOME / '.augmentcode' / 'chats',
        USER_HOME / 'Library' / 'Application Support' / 'augmentcode' / 'chats',
    ]
}

# Windows注册表路径
REGISTRY_PATHS = [
    r'HKEY_CURRENT_USER\Software\augmentcode',
    r'HKEY_CURRENT_USER\Software\JetBrains\*\augment*',
    r'HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Uninstall\augment*',
    r'HKEY_LOCAL_MACHINE\SOFTWARE\augmentcode',
    r'HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\augmentcode',
]

# 文件扩展名和模式匹配
AUGMENT_FILE_PATTERNS = [
    '*augment*',
    '*.augment',
    'augment.*',
    '*augmentcode*',
    '*.augmentcode',
    'augmentcode.*',
]

# 目录名模式匹配
AUGMENT_DIR_PATTERNS = [
    'augment*',
    '*augment*',
    'augmentcode*',
    '*augmentcode*',
    '.augment*',
]

# 默认路径配置
DEFAULT_PATHS = {
    'jetbrains_config': SUPPORTED_IDES['jetbrains']['config_paths'].get(SYSTEM, []),
    'vscode_config': SUPPORTED_IDES['vscode']['config_paths'].get(SYSTEM, []),
    'cursor_config': SUPPORTED_IDES['cursor']['config_paths'].get(SYSTEM, []),
    'augment_paths': AUGMENT_PATHS.get(SYSTEM, []),
    'chat_paths': CHAT_PATHS.get(SYSTEM, []),
}

# 清理配置
CLEANUP_CONFIG = {
    'backup_before_delete': True,  # 删除前是否备份
    'backup_dir': USER_HOME / '.augment_cleaner_backup',
    'max_backup_size': 100 * 1024 * 1024,  # 100MB
    'confirm_dangerous_operations': True,
    'skip_system_files': True,
    'max_file_age_days': 0,  # 0表示清理所有文件，不考虑年龄
}

# 验证配置
VALIDATION_CONFIG = {
    'check_residual_files': True,
    'check_registry_entries': True,
    'check_running_processes': True,
    'generate_report': True,
}

# 日志配置
LOG_CONFIG = {
    'log_file': USER_HOME / '.augment_cleaner.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 3,
    'log_level': 'INFO',
}

# 颜色配置
COLORS = {
    'red': '\033[91m',
    'green': '\033[92m',
    'yellow': '\033[93m',
    'blue': '\033[94m',
    'magenta': '\033[95m',
    'cyan': '\033[96m',
    'white': '\033[97m',
    'reset': '\033[0m',
    'bold': '\033[1m',
}

# 如果在Windows且不支持ANSI颜色，禁用颜色
if IS_WINDOWS:
    try:
        import colorama
        colorama.init()
    except ImportError:
        # 如果没有colorama，在Windows上禁用颜色
        COLORS = {key: '' for key in COLORS}
